{"name": "innoventory", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/postcss7-compat": "^2.2.17", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.523.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "react-router-dom": "^7.6.2", "recharts": "^3.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}