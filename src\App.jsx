import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Vendors from './pages/Vendors';
import Clients from './pages/Clients';
import Orders from './pages/Orders';
import Audit from './pages/Audit';
import SubAdmins from './pages/SubAdmins';
import Settings from './pages/Settings';
import NotFound from './components/NotFound';

function App() {
  return (
    <Router>
      <Routes>
        {/* Main app routes with layout */}
        <Route path="/" element={<Layout><Dashboard /></Layout>} />
        <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
        <Route path="/vendors" element={<Layout><Vendors /></Layout>} />
        <Route path="/clients" element={<Layout><Clients /></Layout>} />
        <Route path="/orders" element={<Layout><Orders /></Layout>} />
        <Route path="/audit" element={<Layout><Audit /></Layout>} />
        <Route path="/sub-admins" element={<Layout><SubAdmins /></Layout>} />
        <Route path="/settings" element={<Layout><Settings /></Layout>} />

        {/* 404 page without layout */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;
