"use strict";

exports.__esModule = true;
exports.default = void 0;

var _declaration = _interopRequireDefault(require("./declaration"));

var _tokenize = _interopRequireDefault(require("./tokenize"));

var _comment = _interopRequireDefault(require("./comment"));

var _atRule = _interopRequireDefault(require("./at-rule"));

var _root = _interopRequireDefault(require("./root"));

var _rule = _interopRequireDefault(require("./rule"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var Parser = /*#__PURE__*/function () {
  function Parser(input) {
    this.input = input;
    this.root = new _root.default();
    this.current = this.root;
    this.spaces = '';
    this.semicolon = false;
    this.createTokenizer();
    this.root.source = {
      input: input,
      start: {
        line: 1,
        column: 1
      }
    };
  }

  var _proto = Parser.prototype;

  _proto.createTokenizer = function createTokenizer() {
    this.tokenizer = (0, _tokenize.default)(this.input);
  };

  _proto.parse = function parse() {
    var token;

    while (!this.tokenizer.endOfFile()) {
      token = this.tokenizer.nextToken();

      switch (token[0]) {
        case 'space':
          this.spaces += token[1];
          break;

        case ';':
          this.freeSemicolon(token);
          break;

        case '}':
          this.end(token);
          break;

        case 'comment':
          this.comment(token);
          break;

        case 'at-word':
          this.atrule(token);
          break;

        case '{':
          this.emptyRule(token);
          break;

        default:
          this.other(token);
          break;
      }
    }

    this.endFile();
  };

  _proto.comment = function comment(token) {
    var node = new _comment.default();
    this.init(node, token[2], token[3]);
    node.source.end = {
      line: token[4],
      column: token[5]
    };
    var text = token[1].slice(2, -2);

    if (/^\s*$/.test(text)) {
      node.text = '';
      node.raws.left = text;
      node.raws.right = '';
    } else {
      var match = text.match(/^(\s*)([^]*[^\s])(\s*)$/);
      node.text = match[2];
      node.raws.left = match[1];
      node.raws.right = match[3];
    }
  };

  _proto.emptyRule = function emptyRule(token) {
    var node = new _rule.default();
    this.init(node, token[2], token[3]);
    node.selector = '';
    node.raws.between = '';
    this.current = node;
  };

  _proto.other = function other(start) {
    var end = false;
    var type = null;
    var colon = false;
    var bracket = null;
    var brackets = [];
    var tokens = [];
    var token = start;

    while (token) {
      type = token[0];
      tokens.push(token);

      if (type === '(' || type === '[') {
        if (!bracket) bracket = token;
        brackets.push(type === '(' ? ')' : ']');
      } else if (brackets.length === 0) {
        if (type === ';') {
          if (colon) {
            this.decl(tokens);
            return;
          } else {
            break;
          }
        } else if (type === '{') {
          this.rule(tokens);
          return;
        } else if (type === '}') {
          this.tokenizer.back(tokens.pop());
          end = true;
          break;
        } else if (type === ':') {
          colon = true;
        }
      } else if (type === brackets[brackets.length - 1]) {
        brackets.pop();
        if (brackets.length === 0) bracket = null;
      }

      token = this.tokenizer.nextToken();
    }

    if (this.tokenizer.endOfFile()) end = true;
    if (brackets.length > 0) this.unclosedBracket(bracket);

    if (end && colon) {
      while (tokens.length) {
        token = tokens[tokens.length - 1][0];
        if (token !== 'space' && token !== 'comment') break;
        this.tokenizer.back(tokens.pop());
      }

      this.decl(tokens);
    } else {
      this.unknownWord(tokens);
    }
  };

  _proto.rule = function rule(tokens) {
    tokens.pop();
    var node = new _rule.default();
    this.init(node, tokens[0][2], tokens[0][3]);
    node.raws.between = this.spacesAndCommentsFromEnd(tokens);
    this.raw(node, 'selector', tokens);
    this.current = node;
  };

  _proto.decl = function decl(tokens) {
    var node = new _declaration.default();
    this.init(node);
    var last = tokens[tokens.length - 1];

    if (last[0] === ';') {
      this.semicolon = true;
      tokens.pop();
    }

    if (last[4]) {
      node.source.end = {
        line: last[4],
        column: last[5]
      };
    } else {
      node.source.end = {
        line: last[2],
        column: last[3]
      };
    }

    while (tokens[0][0] !== 'word') {
      if (tokens.length === 1) this.unknownWord(tokens);
      node.raws.before += tokens.shift()[1];
    }

    node.source.start = {
      line: tokens[0][2],
      column: tokens[0][3]
    };
    node.prop = '';

    while (tokens.length) {
      var type = tokens[0][0];

      if (type === ':' || type === 'space' || type === 'comment') {
        break;
      }

      node.prop += tokens.shift()[1];
    }

    node.raws.between = '';
    var token;

    while (tokens.length) {
      token = tokens.shift();

      if (token[0] === ':') {
        node.raws.between += token[1];
        break;
      } else {
        if (token[0] === 'word' && /\w/.test(token[1])) {
          this.unknownWord([token]);
        }

        node.raws.between += token[1];
      }
    }

    if (node.prop[0] === '_' || node.prop[0] === '*') {
      node.raws.before += node.prop[0];
      node.prop = node.prop.slice(1);
    }

    node.raws.between += this.spacesAndCommentsFromStart(tokens);
    this.precheckMissedSemicolon(tokens);

    for (var i = tokens.length - 1; i > 0; i--) {
      token = tokens[i];

      if (token[1].toLowerCase() === '!important') {
        node.important = true;
        var string = this.stringFrom(tokens, i);
        string = this.spacesFromEnd(tokens) + string;
        if (string !== ' !important') node.raws.important = string;
        break;
      } else if (token[1].toLowerCase() === 'important') {
        var cache = tokens.slice(0);
        var str = '';

        for (var j = i; j > 0; j--) {
          var _type = cache[j][0];

          if (str.trim().indexOf('!') === 0 && _type !== 'space') {
            break;
          }

          str = cache.pop()[1] + str;
        }

        if (str.trim().indexOf('!') === 0) {
          node.important = true;
          node.raws.important = str;
          tokens = cache;
        }
      }

      if (token[0] !== 'space' && token[0] !== 'comment') {
        break;
      }
    }

    this.raw(node, 'value', tokens);
    if (node.value.indexOf(':') !== -1) this.checkMissedSemicolon(tokens);
  };

  _proto.atrule = function atrule(token) {
    var node = new _atRule.default();
    node.name = token[1].slice(1);

    if (node.name === '') {
      this.unnamedAtrule(node, token);
    }

    this.init(node, token[2], token[3]);
    var prev;
    var shift;
    var last = false;
    var open = false;
    var params = [];

    while (!this.tokenizer.endOfFile()) {
      token = this.tokenizer.nextToken();

      if (token[0] === ';') {
        node.source.end = {
          line: token[2],
          column: token[3]
        };
        this.semicolon = true;
        break;
      } else if (token[0] === '{') {
        open = true;
        break;
      } else if (token[0] === '}') {
        if (params.length > 0) {
          shift = params.length - 1;
          prev = params[shift];

          while (prev && prev[0] === 'space') {
            prev = params[--shift];
          }

          if (prev) {
            node.source.end = {
              line: prev[4],
              column: prev[5]
            };
          }
        }

        this.end(token);
        break;
      } else {
        params.push(token);
      }

      if (this.tokenizer.endOfFile()) {
        last = true;
        break;
      }
    }

    node.raws.between = this.spacesAndCommentsFromEnd(params);

    if (params.length) {
      node.raws.afterName = this.spacesAndCommentsFromStart(params);
      this.raw(node, 'params', params);

      if (last) {
        token = params[params.length - 1];
        node.source.end = {
          line: token[4],
          column: token[5]
        };
        this.spaces = node.raws.between;
        node.raws.between = '';
      }
    } else {
      node.raws.afterName = '';
      node.params = '';
    }

    if (open) {
      node.nodes = [];
      this.current = node;
    }
  };

  _proto.end = function end(token) {
    if (this.current.nodes && this.current.nodes.length) {
      this.current.raws.semicolon = this.semicolon;
    }

    this.semicolon = false;
    this.current.raws.after = (this.current.raws.after || '') + this.spaces;
    this.spaces = '';

    if (this.current.parent) {
      this.current.source.end = {
        line: token[2],
        column: token[3]
      };
      this.current = this.current.parent;
    } else {
      this.unexpectedClose(token);
    }
  };

  _proto.endFile = function endFile() {
    if (this.current.parent) this.unclosedBlock();

    if (this.current.nodes && this.current.nodes.length) {
      this.current.raws.semicolon = this.semicolon;
    }

    this.current.raws.after = (this.current.raws.after || '') + this.spaces;
  };

  _proto.freeSemicolon = function freeSemicolon(token) {
    this.spaces += token[1];

    if (this.current.nodes) {
      var prev = this.current.nodes[this.current.nodes.length - 1];

      if (prev && prev.type === 'rule' && !prev.raws.ownSemicolon) {
        prev.raws.ownSemicolon = this.spaces;
        this.spaces = '';
      }
    }
  } // Helpers
  ;

  _proto.init = function init(node, line, column) {
    this.current.push(node);
    node.source = {
      start: {
        line: line,
        column: column
      },
      input: this.input
    };
    node.raws.before = this.spaces;
    this.spaces = '';
    if (node.type !== 'comment') this.semicolon = false;
  };

  _proto.raw = function raw(node, prop, tokens) {
    var token, type;
    var length = tokens.length;
    var value = '';
    var clean = true;
    var next, prev;
    var pattern = /^([.|#])?([\w])+/i;

    for (var i = 0; i < length; i += 1) {
      token = tokens[i];
      type = token[0];

      if (type === 'comment' && node.type === 'rule') {
        prev = tokens[i - 1];
        next = tokens[i + 1];

        if (prev[0] !== 'space' && next[0] !== 'space' && pattern.test(prev[1]) && pattern.test(next[1])) {
          value += token[1];
        } else {
          clean = false;
        }

        continue;
      }

      if (type === 'comment' || type === 'space' && i === length - 1) {
        clean = false;
      } else {
        value += token[1];
      }
    }

    if (!clean) {
      var raw = tokens.reduce(function (all, i) {
        return all + i[1];
      }, '');
      node.raws[prop] = {
        value: value,
        raw: raw
      };
    }

    node[prop] = value;
  };

  _proto.spacesAndCommentsFromEnd = function spacesAndCommentsFromEnd(tokens) {
    var lastTokenType;
    var spaces = '';

    while (tokens.length) {
      lastTokenType = tokens[tokens.length - 1][0];
      if (lastTokenType !== 'space' && lastTokenType !== 'comment') break;
      spaces = tokens.pop()[1] + spaces;
    }

    return spaces;
  };

  _proto.spacesAndCommentsFromStart = function spacesAndCommentsFromStart(tokens) {
    var next;
    var spaces = '';

    while (tokens.length) {
      next = tokens[0][0];
      if (next !== 'space' && next !== 'comment') break;
      spaces += tokens.shift()[1];
    }

    return spaces;
  };

  _proto.spacesFromEnd = function spacesFromEnd(tokens) {
    var lastTokenType;
    var spaces = '';

    while (tokens.length) {
      lastTokenType = tokens[tokens.length - 1][0];
      if (lastTokenType !== 'space') break;
      spaces = tokens.pop()[1] + spaces;
    }

    return spaces;
  };

  _proto.stringFrom = function stringFrom(tokens, from) {
    var result = '';

    for (var i = from; i < tokens.length; i++) {
      result += tokens[i][1];
    }

    tokens.splice(from, tokens.length - from);
    return result;
  };

  _proto.colon = function colon(tokens) {
    var brackets = 0;
    var token, type, prev;

    for (var i = 0; i < tokens.length; i++) {
      token = tokens[i];
      type = token[0];

      if (type === '(') {
        brackets += 1;
      }

      if (type === ')') {
        brackets -= 1;
      }

      if (brackets === 0 && type === ':') {
        if (!prev) {
          this.doubleColon(token);
        } else if (prev[0] === 'word' && prev[1] === 'progid') {
          continue;
        } else {
          return i;
        }
      }

      prev = token;
    }

    return false;
  } // Errors
  ;

  _proto.unclosedBracket = function unclosedBracket(bracket) {
    throw this.input.error('Unclosed bracket', bracket[2], bracket[3]);
  };

  _proto.unknownWord = function unknownWord(tokens) {
    throw this.input.error('Unknown word', tokens[0][2], tokens[0][3]);
  };

  _proto.unexpectedClose = function unexpectedClose(token) {
    throw this.input.error('Unexpected }', token[2], token[3]);
  };

  _proto.unclosedBlock = function unclosedBlock() {
    var pos = this.current.source.start;
    throw this.input.error('Unclosed block', pos.line, pos.column);
  };

  _proto.doubleColon = function doubleColon(token) {
    throw this.input.error('Double colon', token[2], token[3]);
  };

  _proto.unnamedAtrule = function unnamedAtrule(node, token) {
    throw this.input.error('At-rule without name', token[2], token[3]);
  };

  _proto.precheckMissedSemicolon = function precheckMissedSemicolon()
  /* tokens */
  {// Hook for Safe Parser
  };

  _proto.checkMissedSemicolon = function checkMissedSemicolon(tokens) {
    var colon = this.colon(tokens);
    if (colon === false) return;
    var founded = 0;
    var token;

    for (var j = colon - 1; j >= 0; j--) {
      token = tokens[j];

      if (token[0] !== 'space') {
        founded += 1;
        if (founded === 2) break;
      }
    }

    throw this.input.error('Missed semicolon', token[2], token[3]);
  };

  return Parser;
}();

exports.default = Parser;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
