import { useState } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';

const Orders = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    client: '',
    vendor: '',
    orderType: '',
    priority: '',
    description: '',
    deliveryAddress: '',
    specialInstructions: '',
    items: [{ name: '', quantity: '', unitPrice: '', total: '' }]
  });
  const [uploadedFiles, setUploadedFiles] = useState([]);

  // Sample order data
  const orders = [
    {
      id: 'ORD-001',
      client: 'Acme Corporation',
      vendor: 'ABC Supplies',
      amount: '$2,450.00',
      status: 'Completed',
      orderDate: '2024-06-20',
      deliveryDate: '2024-06-25',
      items: 5,
      priority: 'High',
      orderType: 'Purchase Order',
      paymentStatus: 'Paid',
      shippingMethod: 'Express'
    },
    {
      id: 'ORD-002',
      client: 'Global Tech Inc',
      vendor: 'XYZ Manufacturing',
      amount: '$1,890.00',
      status: 'Processing',
      orderDate: '2024-06-22',
      deliveryDate: '2024-06-28',
      items: 3,
      priority: 'Medium',
      orderType: 'Service Order',
      paymentStatus: 'Pending',
      shippingMethod: 'Standard'
    },
    {
      id: 'ORD-003',
      client: 'StartUp Solutions',
      vendor: 'Tech Solutions Ltd',
      amount: '$750.00',
      status: 'Pending',
      orderDate: '2024-06-24',
      deliveryDate: '2024-06-30',
      items: 2,
      priority: 'Low',
      orderType: 'Maintenance Order',
      paymentStatus: 'Pending',
      shippingMethod: 'Standard'
    },
  ];

  const columns = [
    {
      key: 'id',
      label: 'Order ID',
      sortable: true,
      filterable: true
    },
    {
      key: 'client',
      label: 'Client',
      sortable: true,
      filterable: true
    },
    {
      key: 'vendor',
      label: 'Vendor',
      sortable: true,
      filterable: true
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      filterable: false
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Completed' ? 'bg-green-100 text-green-800' :
          value === 'Processing' ? 'bg-blue-100 text-blue-800' :
          value === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'High' ? 'bg-red-100 text-red-800' :
          value === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'orderDate',
      label: 'Order Date',
      sortable: true,
      filterable: false
    },
    {
      key: 'deliveryDate',
      label: 'Delivery Date',
      sortable: true,
      filterable: false
    },
    {
      key: 'items',
      label: 'Items',
      sortable: true,
      filterable: false
    },
    {
      key: 'paymentStatus',
      label: 'Payment Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (value, row) => (
        <div className="flex space-x-2">
          <button className="text-blue-600 hover:text-blue-900 text-sm">
            View
          </button>
          <button className="text-blue-600 hover:text-blue-900 text-sm">
            Edit
          </button>
          <button className="text-red-600 hover:text-red-900 text-sm">
            Cancel
          </button>
        </div>
      )
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items];
    newItems[index][field] = value;

    // Calculate total for the item
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = parseFloat(newItems[index].quantity) || 0;
      const unitPrice = parseFloat(newItems[index].unitPrice) || 0;
      newItems[index].total = (quantity * unitPrice).toFixed(2);
    }

    setFormData(prev => ({
      ...prev,
      items: newItems
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { name: '', quantity: '', unitPrice: '', total: '' }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const handleFilesChange = (files) => {
    setUploadedFiles(files);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form Data:', formData);
    console.log('Uploaded Files:', uploadedFiles);
    // Here you would typically send the data to your backend
    setShowAddForm(false);
    // Reset form
    setFormData({
      client: '',
      vendor: '',
      orderType: '',
      priority: '',
      description: '',
      deliveryAddress: '',
      specialInstructions: '',
      items: [{ name: '', quantity: '', unitPrice: '', total: '' }]
    });
    setUploadedFiles([]);
  };



  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
          <p className="mt-2 text-gray-600">Track and manage all orders</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add New Order
        </button>
      </div>

      {/* Orders Table */}
      <DataTable
        data={orders}
        columns={columns}
        title="Orders Management"
        defaultPageSize={50}
        enableExport={true}
        enableColumnToggle={true}
        enableFiltering={true}
        enableSorting={true}
      />

      {/* Add Order Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowAddForm(false)}></div>
            <div className="relative bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Create New Order</h3>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Order Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Order Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client *
                      </label>
                      <select
                        name="client"
                        value={formData.client}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select client</option>
                        <option value="Acme Corporation">Acme Corporation</option>
                        <option value="Global Tech Inc">Global Tech Inc</option>
                        <option value="StartUp Solutions">StartUp Solutions</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Vendor *
                      </label>
                      <select
                        name="vendor"
                        value={formData.vendor}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select vendor</option>
                        <option value="ABC Supplies">ABC Supplies</option>
                        <option value="XYZ Manufacturing">XYZ Manufacturing</option>
                        <option value="Tech Solutions Ltd">Tech Solutions Ltd</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Order Type *
                      </label>
                      <select
                        name="orderType"
                        value={formData.orderType}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select order type</option>
                        <option value="Purchase Order">Purchase Order</option>
                        <option value="Service Order">Service Order</option>
                        <option value="Maintenance Order">Maintenance Order</option>
                        <option value="Emergency Order">Emergency Order</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Priority *
                      </label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select priority</option>
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  {/* Delivery Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Delivery Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Delivery Address *
                      </label>
                      <textarea
                        name="deliveryAddress"
                        value={formData.deliveryAddress}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="input-field"
                        placeholder="Complete delivery address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Special Instructions
                      </label>
                      <textarea
                        name="specialInstructions"
                        value={formData.specialInstructions}
                        onChange={handleInputChange}
                        rows={3}
                        className="input-field"
                        placeholder="Any special delivery or handling instructions"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Order Description
                      </label>
                      <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={3}
                        className="input-field"
                        placeholder="Brief description of the order"
                      />
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-medium text-gray-900">Order Items</h4>
                    <button
                      type="button"
                      onClick={addItem}
                      className="btn-secondary text-sm"
                    >
                      Add Item
                    </button>
                  </div>

                  <div className="space-y-3">
                    {formData.items.map((item, index) => (
                      <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 border border-gray-200 rounded-lg">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Item Name *
                          </label>
                          <input
                            type="text"
                            value={item.name}
                            onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                            required
                            className="input-field text-sm"
                            placeholder="Item name"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Quantity *
                          </label>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                            required
                            className="input-field text-sm"
                            placeholder="Qty"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Unit Price *
                          </label>
                          <input
                            type="number"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', e.target.value)}
                            required
                            className="input-field text-sm"
                            placeholder="Price"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Total
                          </label>
                          <input
                            type="text"
                            value={item.total ? `$${item.total}` : ''}
                            readOnly
                            className="input-field text-sm bg-gray-50"
                            placeholder="$0.00"
                          />
                        </div>
                        <div className="flex items-end">
                          {formData.items.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              className="text-red-600 hover:text-red-900 text-sm px-2 py-1"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Order Total */}
                  <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-900">Order Total:</span>
                      <span className="text-lg font-bold text-gray-900">
                        ${formData.items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* File Upload Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Order Documents</h4>
                  <FileUpload
                    onFilesChange={handleFilesChange}
                    existingFiles={uploadedFiles}
                    maxFileSize={5}
                    multiple={true}
                    label="Upload Order Documents"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Upload purchase orders, specifications, drawings, contracts, etc.
                  </p>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    Create Order
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;
