{"name": "postcss", "version": "6.0.23", "description": "Tool for transforming styles with JS plugins", "engines": {"node": ">=4.0.0"}, "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://postcss.org/", "repository": "postcss/postcss", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "main": "lib/postcss", "types": "lib/postcss.d.ts", "browser": {"supports-color": false, "chalk": false, "fs": false}}